# Binaries
ccr
claude-code-router
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Configuration files (contain sensitive data)
config.json
.claude-code-router/

# PID and reference files
*.pid
*reference-count.txt

# Temporary directories
tmp/
.tmp/
temp/

# Air hot reload
build-errors.log
