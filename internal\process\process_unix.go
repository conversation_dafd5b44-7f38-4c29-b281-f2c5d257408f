//go:build unix || linux || darwin

package process

import (
	"syscall"
)

// isProcessRunning checks if a process with the given PID is running on Unix systems.
// On Unix, os.FindProcess always succeeds, so we need to send signal 0 to test if the process exists.
func isProcessRunning(pid int) bool {
	// Send signal 0 to check if process exists without actually sending a signal
	err := syscall.Kill(pid, 0)
	return err == nil
}

// killProcess terminates a process with the given PID on Unix systems.
// It sends SIGTERM to gracefully terminate the process.
func killProcess(pid int) error {
	return syscall.Kill(pid, syscall.SIGTERM)
}