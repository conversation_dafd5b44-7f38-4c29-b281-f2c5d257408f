//go:build windows

package process

import (
	"os"
	"syscall"
)

var (
	kernel32         = syscall.NewLazyDLL("kernel32.dll")
	procOpenProcess  = kernel32.NewProc("OpenProcess")
	procCloseHandle  = kernel32.NewProc("CloseHandle")
)

// isProcessRunning checks if a process with the given PID is running on Windows.
// On Windows, we try to open the process handle to verify it exists.
func isProcessRunning(pid int) bool {
	// Try to open the process with minimal access rights
	// PROCESS_QUERY_LIMITED_INFORMATION (0x1000) is sufficient to check if process exists
	handle, _, _ := procOpenProcess.Call(0x1000, 0, uintptr(pid))
	if handle == 0 {
		return false
	}

	// Close the handle to avoid resource leak
	procCloseHandle.Call(handle)
	return true
}

// killProcess terminates a process with the given PID on Windows.
// It uses the Process.Kill method which forcefully terminates the process.
// On Windows, os.FindProcess always succeeds, so we need to check if the process
// actually exists before attempting to kill it.
func killProcess(pid int) error {
	// First check if the process exists
	if !isProcessRunning(pid) {
		return nil // Process doesn't exist, nothing to kill
	}

	// Find the process and kill it
	process, err := os.FindProcess(pid)
	if err != nil {
		return err
	}

	return process.Kill()
}
