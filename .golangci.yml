run:
  timeout: 5m
  go: "1.22"
  modules-download-mode: readonly

linters-settings:
  govet:
    check-shadowing: true
    settings:
      printf:
        funcs:
          - (log/slog.Logger).Debug
          - (log/slog.Logger).Info
          - (log/slog.Logger).Warn
          - (log/slog.Logger).Error

  gocyclo:
    min-complexity: 15

  maligned:
    suggest-new: true

  dupl:
    threshold: 100

  goconst:
    min-len: 2
    min-occurrences: 2

  misspell:
    locale: US

  lll:
    line-length: 140

  goimports:
    local-prefixes: github.com/Davincible/claude-code-router-go

  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

  funlen:
    lines: 100
    statements: 50

  gosec:
    excludes:
      - G204  # Subprocess launched with variable
      - G301  # Poor file permissions used when creating a directory
      - G302  # Poor file permissions used with chmod
      - G304  # File path provided as taint input

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - errcheck
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - misspell
    - nakedret
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
        - funlen
        - goconst

    - path: cmd/
      linters:
        - gomnd

    - path: internal/
      text: "G204:"
      linters:
        - gosec

  exclude-use-default: false
  exclude:
    - 'declaration of "(err|ctx)" shadows declaration at'