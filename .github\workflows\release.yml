name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.22

    - name: Run tests
      run: go test -v ./...

    - name: Build binaries
      run: |
        mkdir -p build
        
        # Build for multiple platforms
        platforms=("linux/amd64" "linux/arm64" "darwin/amd64" "darwin/arm64" "windows/amd64")
        
        for platform in "${platforms[@]}"; do
          IFS='/' read -r goos goarch <<< "$platform"
          output="build/ccr-${goos}-${goarch}"
          
          if [ "$goos" = "windows" ]; then
            output="${output}.exe"
          fi
          
          echo "Building for $goos/$goarch..."
          GOOS="$goos" GOARCH="$goarch" go build \
            -ldflags="-s -w -X 'github.com/Davincible/claude-code-router-go/cmd.Version=${GITHUB_REF#refs/tags/}'" \
            -o "$output" \
            .
        done

    - name: Generate checksums
      run: |
        cd build
        sha256sum * > checksums.txt
        cat checksums.txt

    - name: Create release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          build/*
        body: |
          ## Claude Code Router ${{ github.ref_name }}
          
          ### Installation
          
          Download the appropriate binary for your platform and add it to your PATH.
          
          ### Linux/macOS
          ```bash
          # Download and install (replace with your platform)
          wget https://github.com/Davincible/claude-code-router-go/releases/download/${{ github.ref_name }}/ccr-linux-amd64
          chmod +x ccr-linux-amd64
          sudo mv ccr-linux-amd64 /usr/local/bin/ccr
          ```
          
          ### Windows
          ```powershell
          # Download ccr-windows-amd64.exe and add to PATH
          ```
          
          ### Quick Start
          ```bash
          # Initialize configuration
          ccr config init
          
          # Start the router
          ccr start
          
          # Use with Claude Code
          ccr code
          ```
          
          ### Checksums
          
          Verify your download with the checksums in `checksums.txt`.
          
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}